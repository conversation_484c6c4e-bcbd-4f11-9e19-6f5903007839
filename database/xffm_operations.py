#!/usr/bin/env python3
"""
XFFM database operations.

This module provides functionality for saving XFFM data to the database.
"""

import os
import sys

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.base_operations import BaseDatabaseOperations


class XFFMDatabaseOperations(BaseDatabaseOperations):
    """
    Database operations for XFFM (Flight Manifest) data.

    This class handles saving XFFM data to the database including
    flight manifests, ULDs, and waybill allocations.
    """

    def __init__(self, db_connection, db_cursor, user_id=1, branch_id=1, logger=None):
        """
        Initialize XFFM database operations.

        Args:
            db_connection: Database connection object.
            db_cursor: Database cursor object.
            user_id (int): User ID for audit fields.
            branch_id (int): Branch ID for audit fields.
            logger (logging.Logger): Logger instance.
        """
        super().__init__(db_connection, db_cursor, logger)
        self.user_id = user_id
        self.branch_id = branch_id

    def save_data(self, data):
        """
        Save XFFM data to the database.

        Args:
            data (dict): Extracted and validated XFFM data.

        Returns:
            dict: Result of save operation including IDs and status.
        """
        result = {
            "success": False,
            "manifest_id": None,
            "uld_ids": [],
            "awb_ids": [],
            "errors": [],
            "warnings": [],
        }

        try:
            # Check if manifest already exists
            existing_manifest = self.find_existing_manifest(data["manifest_id"])
            if existing_manifest:
                result["errors"].append(
                    f"Manifest {data['manifest_id']} already exists"
                )
                return result

            # Save flight manifest
            manifest_id = self.save_flight_manifest(data)
            result["manifest_id"] = manifest_id

            # Track AWBs that have been saved to avoid duplicates (like old parser)
            saved_awbs = set()

            # Save ULDs and their AWB allocations
            uld_ids, uld_awb_ids = self.save_ulds(
                manifest_id, data.get("ulds", []), saved_awbs
            )
            result["uld_ids"] = uld_ids

            # Save standalone waybills as placeholder master waybills if they don't exist
            # Skip AWBs that were already saved via ULD processing
            awb_ids = self.save_waybills(
                manifest_id, data.get("waybills", []), saved_awbs
            )
            result["awb_ids"] = awb_ids + uld_awb_ids

            # Commit transaction
            self.commit_transaction()

            result["success"] = True
            self.logger.info(
                f"Successfully saved XFFM data for manifest {data['manifest_id']} "
                f"with {len(uld_ids)} ULDs and {len(result['awb_ids'])} AWBs"
            )

        except Exception as e:
            # Rollback transaction on error
            self.rollback_transaction()
            error_msg = f"Error saving XFFM data: {e}"
            result["errors"].append(error_msg)
            self.logger.error(error_msg)

        return result

    def find_existing_manifest(self, manifest_id):
        """
        Check if a flight manifest already exists in the database.

        Args:
            manifest_id (str): Manifest ID to check.

        Returns:
            tuple: Existing manifest record or None.
        """
        return self.find_record(
            "flight_manifests",
            "manifest_id = %s",
            (manifest_id,),
            "manifest_id, status",
        )

    def save_flight_manifest(self, data):
        """
        Save flight manifest data.

        Args:
            data (dict): XFFM data.

        Returns:
            int: Manifest ID of the saved record.
        """
        # Prepare flight manifest data
        manifest_data = {
            "manifest_id": data["manifest_id"],
            "flight_number": data["flight_number"],
            "flight_date": data["flight_date"],
            "carrier_code": data["carrier_code"],
            "departure_airport": data["departure_airport"],
            "arrival_airport": data["arrival_airport"],
            "scheduled_departure": data["scheduled_departure"],
            "scheduled_arrival": data["scheduled_arrival"],
            "actual_departure": data.get("actual_departure"),
            "actual_arrival": data.get("actual_arrival"),
            "flight_type": data.get("flight_type", "NORMAL"),
            "status": data.get("status", "SCHEDULED"),
            "total_pieces": data.get("total_pieces", 0),
            "total_weight": data.get("total_weight", 0.0),
            "sync_status": data.get("sync_status", "PENDING"),
            "branch_id": self.branch_id,
        }

        # Store complete original XML content for audit trails and reprocessing
        # Following OLD-PARSER format: json.dumps({'xml': xml_string})
        xml_content = data.get("xml_content")
        if xml_content:
            import json

            # Store XML content in OLD-PARSER compatible format
            manifest_data["xml_data"] = json.dumps({"xml": xml_content})
            self.logger.info(
                f"Storing original XML content ({len(xml_content)} characters) for manifest {data['manifest_id']}"
            )

        # Add timestamps and audit fields
        self.add_timestamps(manifest_data, self.user_id, self.user_id)

        # Insert flight manifest (manifest_id is the primary key, not auto-increment)
        self.insert_record("flight_manifests", manifest_data)
        manifest_id = manifest_data["manifest_id"]  # Use the manifest_id we set

        self.logger.info(f"Saved flight manifest with ID {manifest_id}")
        return manifest_id

    def save_ulds(self, manifest_id, ulds, saved_awbs=None):
        """
        Save ULD data and their AWB allocations.

        Args:
            manifest_id (int): Flight manifest ID.
            ulds (list): List of ULD data.
            saved_awbs (set): Set to track AWBs that have been saved to avoid duplicates.

        Returns:
            tuple: (List of ULD IDs, List of AWB IDs created from ULD processing).
        """
        if saved_awbs is None:
            saved_awbs = set()

        uld_ids = []
        uld_awb_ids = []

        for uld_data in ulds:
            uld_id, awb_ids = self.save_single_uld(manifest_id, uld_data, saved_awbs)
            if uld_id:
                uld_ids.append(uld_id)
                uld_awb_ids.extend(awb_ids)

        return uld_ids, uld_awb_ids

    def save_single_uld(self, manifest_id, uld_data, saved_awbs):
        """
        Save a single ULD and its AWB allocations.

        Args:
            manifest_id (int): Flight manifest ID.
            uld_data (dict): ULD data.
            saved_awbs (set): Set to track AWBs that have been saved to avoid duplicates.

        Returns:
            tuple: (ULD record ID, List of AWB IDs created).
        """
        # Prepare ULD data
        uld_record = {
            "manifest_id": manifest_id,
            "uld_id": uld_data["uld_id"],
            "uld_type": uld_data.get("uld_type", ""),
            "uld_owner": uld_data.get("uld_owner", ""),
            "loading_indicator": uld_data.get("loading_indicator", ""),
            "weight": uld_data.get("weight", 0.0),
            "pieces": uld_data.get("pieces", 0),
            "status": "ACTIVE",
            "branch_id": self.branch_id,
        }

        # Add timestamps and audit fields
        self.add_timestamps(uld_record, self.user_id, self.user_id)

        # Insert ULD record
        uld_record_id = self.insert_record("uld_details", uld_record, "id")

        # Create ULD movement record for arrival/loading
        self.create_uld_movement(
            uld_data["uld_id"],
            manifest_id,
            "ARRIVAL",
            to_status="LOADED",
            notes=f"ULD loaded on flight manifest {manifest_id}",
        )

        # Save AWB allocations for this ULD and create master waybills
        awbs = uld_data.get("awbs", [])
        uld_saved_awbs = set()  # Track AWBs within this ULD to avoid duplicates
        awb_ids = []

        for awb_data in awbs:
            awb_number = awb_data.get("awb_number")

            # Skip duplicates within this ULD
            if awb_number in uld_saved_awbs:
                self.logger.warning(
                    f"Skipping duplicate AWB {awb_number} within ULD {uld_data['uld_id']}"
                )
                continue

            # Save ULD allocation
            self.save_uld_awb_allocation(uld_record_id, awb_data)

            # Create master waybill if not already saved (like old parser)
            if awb_number not in saved_awbs:
                awb_id = self.save_single_waybill(manifest_id, awb_data)
                if awb_id:
                    awb_ids.append(awb_id)
                    saved_awbs.add(awb_number)

            uld_saved_awbs.add(awb_number)

        self.logger.info(
            f"Saved ULD {uld_data['uld_id']} with {len(uld_saved_awbs)} AWBs"
        )
        return uld_record_id, awb_ids

    def save_uld_awb_allocation(self, uld_record_id, awb_data):
        """
        Save AWB allocation to ULD and detailed AWB data.

        Args:
            uld_record_id (int): ULD record ID.
            awb_data (dict): AWB data.
        """
        # Get the ULD ID and details from the ULD record
        uld_detail = self.find_record(
            "uld_details", "id = %s", (uld_record_id,), "uld_id, manifest_id, uld_type"
        )

        if not uld_detail:
            self.logger.error(f"ULD record with ID {uld_record_id} not found")
            return None

        uld_id, manifest_id, uld_type = uld_detail

        # Save to uld_awb_allocations table
        allocation_data = {
            "uld_id": uld_id,
            "manifest_id": manifest_id,
            "awb_number": awb_data["awb_number"],
            "assigned_pieces": awb_data.get("pieces", 0),
            "assigned_weight": awb_data.get("weight", 0.0),
            "branch_id": self.branch_id,
        }

        # Add timestamps and audit fields
        self.add_timestamps(allocation_data, self.user_id, self.user_id)

        # Insert allocation record
        allocation_id = self.insert_record("uld_awb_allocations", allocation_data, "id")

        # Save detailed AWB data to uld_awbs table (following old parser pattern)
        self.save_uld_awb_details(uld_id, manifest_id, uld_type, awb_data)

        self.logger.info(f"Allocated AWB {awb_data['awb_number']} to ULD {uld_id}")
        return allocation_id

    def save_uld_awb_details(self, uld_id, manifest_id, uld_type, awb_data):
        """
        Save detailed AWB data to uld_awbs table (following old parser pattern).

        Args:
            uld_id (str): ULD ID.
            manifest_id (str): Manifest ID.
            uld_type (str): ULD type.
            awb_data (dict): AWB data.
        """
        awb_number = awb_data.get("awb_number")
        if not awb_number:
            return None

        # Check if AWB already exists in uld_awbs
        existing_awb = self.find_record(
            "uld_awbs",
            "uld_id = %s AND manifest_id = %s AND awb_number = %s",
            (uld_id, manifest_id, awb_number),
            "id",
        )

        # Prepare AWB data for uld_awbs table
        uld_awb_data = {
            "uld_id": uld_id,
            "manifest_id": manifest_id,
            "awb_number": awb_number,
            "uld_type": uld_type,
            "pieces": awb_data.get("pieces", 0),
            "weight": awb_data.get("weight", 0.0),
            "volume": awb_data.get("volume"),  # Legacy volume field
            "gross_volume": awb_data.get("gross_volume"),
            "volume_unit": awb_data.get("volume_unit"),
            "split_code": awb_data.get("transport_split_code"),
            "description": awb_data.get("description"),
            "summary_description": awb_data.get("summary_description"),
            "branch_id": self.branch_id,
        }

        # Ensure volume_unit is None if gross_volume is None
        if uld_awb_data["gross_volume"] is None:
            uld_awb_data["volume_unit"] = None

        if existing_awb:
            # Update existing AWB
            awb_db_id = existing_awb[0]
            self.add_timestamps(uld_awb_data, updated_by=self.user_id)

            self.update_record("uld_awbs", uld_awb_data, "id = %s", (awb_db_id,))

            self.logger.info(f"Updated existing ULD AWB: {awb_number} in ULD {uld_id}")
            return awb_db_id
        else:
            # Insert new AWB
            self.add_timestamps(uld_awb_data, self.user_id, self.user_id)

            awb_db_id = self.insert_record("uld_awbs", uld_awb_data, "id")

            self.logger.info(f"Inserted new ULD AWB: {awb_number} in ULD {uld_id}")
            return awb_db_id

    def create_uld_movement(
        self,
        uld_id,
        manifest_id,
        movement_type,
        from_status=None,
        to_status=None,
        from_location=None,
        to_location=None,
        notes=None,
    ):
        """
        Create a ULD movement record for tracking ULD status changes.

        Args:
            uld_id (str): ULD ID.
            manifest_id (str): Manifest ID.
            movement_type (str): Type of movement (ARRIVAL, DEPARTURE, STATUS_CHANGE, LOCATION_CHANGE).
            from_status (str): Previous status.
            to_status (str): New status.
            from_location (str): Previous location.
            to_location (str): New location.
            notes (str): Movement notes.
        """
        movement_data = {
            "uld_id": uld_id,
            "manifest_id": manifest_id,
            "movement_type": movement_type,
            "from_status": from_status,
            "to_status": to_status,
            "from_location": from_location,
            "to_location": to_location,
            "notes": notes,
            "recorded_by": self.user_id,
        }

        # Insert movement record with current timestamp for movement_time
        # Note: uld_movements table only has created_at/updated_at, not created_by/updated_by
        try:
            # Build the query manually to handle movement_time as NOW()
            columns = list(movement_data.keys()) + [
                "movement_time",
                "created_at",
                "updated_at",
            ]
            placeholders = ["%s"] * len(movement_data) + ["NOW()", "NOW()", "NOW()"]
            values = list(movement_data.values())

            query = f"""
                INSERT INTO uld_movements ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
                RETURNING id
            """

            self.db_cursor.execute(query, values)
            movement_id = self.db_cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"Error inserting ULD movement: {e}")
            raise

        self.logger.info(f"Created ULD movement record for {uld_id}: {movement_type}")
        return movement_id

    def save_waybills(self, manifest_id, waybills, saved_awbs=None):
        """
        Save standalone waybills as placeholder master waybills if they don't exist.
        Handle duplicates gracefully like the old parser.

        Args:
            manifest_id (str): Flight manifest ID.
            waybills (list): List of waybill data.
            saved_awbs (set): Set to track AWBs that have been saved to avoid duplicates.

        Returns:
            list: List of AWB IDs that were created.
        """
        if saved_awbs is None:
            saved_awbs = set()

        awb_ids = []

        for waybill_data in waybills:
            awb_number = waybill_data.get("awb_number")

            # Skip if already saved via ULD AWBs (like old parser)
            if awb_number in saved_awbs:
                self.logger.info(
                    f"Skipping AWB {awb_number} - already saved via ULD processing"
                )
                continue

            awb_id = self.save_single_waybill(manifest_id, waybill_data)
            if awb_id:
                awb_ids.append(awb_id)
                saved_awbs.add(awb_number)

        return awb_ids

    def save_single_waybill(self, manifest_id, waybill_data):
        """
        Save a single waybill as a placeholder master waybill if it doesn't exist.

        Args:
            manifest_id (str): Flight manifest ID.
            waybill_data (dict): Waybill data.

        Returns:
            int: AWB ID if created, None if already exists.
        """
        awb_number = waybill_data.get("awb_number")
        if not awb_number:
            return None

        # Check if this AWB already exists
        existing_awb = self.find_record(
            "master_waybills", "awb_number = %s", (awb_number,), "awb_id"
        )

        if existing_awb:
            self.logger.info(f"AWB {awb_number} already exists, skipping creation")
            return existing_awb[0]

        # Get manifest data for fallback airport codes
        manifest_data = self.find_record(
            "flight_manifests",
            "manifest_id = %s",
            (manifest_id,),
            "departure_airport, arrival_airport",
        )

        fallback_origin = manifest_data[0] if manifest_data else "XXX"
        fallback_destination = manifest_data[1] if manifest_data else "XXX"

        # Determine in-transit status
        origin_airport = waybill_data.get("origin_airport") or fallback_origin
        destination_airport = waybill_data.get("destination_airport") or fallback_destination
        in_transit = self._determine_in_transit_status(origin_airport, destination_airport)

        # Create placeholder master waybill
        awb_data = {
            "awb_number": awb_number,
            "manifest_id": manifest_id,
            "type_code": waybill_data.get("type_code", "AWB"),
            "origin_airport": origin_airport,
            "destination_airport": destination_airport,
            "total_pieces": waybill_data.get("pieces", 0),
            "total_weight": waybill_data.get("weight", 0.0),
            "weight_unit": waybill_data.get("weight_unit", "KGM"),
            "gross_volume": waybill_data.get("gross_volume"),
            "volume_unit": waybill_data.get("volume_unit"),
            "summary_description": waybill_data.get("summary_description"),
            "special_handling_code": waybill_data.get("special_handling_code"),
            "is_mail": waybill_data.get("is_mail", False),
            "is_partial": waybill_data.get("is_partial", False),
            "is_human_remains": waybill_data.get("is_human_remains", False),
            "has_houses": waybill_data.get("has_houses", False),
            "status": waybill_data.get("status", "PENDING"),
            "in_transit": in_transit,
            "branch_id": self.branch_id,
        }

        # Add XML data with split code information for audit trail
        xml_data = waybill_data.get("xml_data", {})
        if isinstance(xml_data, str):
            import json

            try:
                xml_data = json.loads(xml_data)
            except (json.JSONDecodeError, TypeError):
                xml_data = {}

        # Add split code information to XML data for audit purposes
        split_code = waybill_data.get("transport_split_code")
        if split_code:
            xml_data["transport_split_code"] = split_code
            xml_data["split_info"] = {
                "is_partial": waybill_data.get("is_partial", False),
                "is_split_across_ulds": waybill_data.get("is_split_across_ulds", False),
                "is_complete_shipment": waybill_data.get("is_complete_shipment", False),
            }

            # Add partial source information for XFFM
            if waybill_data.get("is_partial", False):
                xml_data["split_info"]["partial_source"] = "XFFM"
                xml_data["split_info"]["created_at"] = self.get_current_timestamp()

            import json

            awb_data["xml_data"] = json.dumps(xml_data)

        # Add timestamps and audit fields
        self.add_timestamps(awb_data, self.user_id, self.user_id)

        # Insert master waybill
        awb_id = self.insert_record("master_waybills", awb_data, "awb_id")

        self.logger.info(
            f"Created placeholder master waybill {awb_number} from XFFM (ID: {awb_id})"
        )

        # Create partial waybill if this is a partial shipment (split code "P" or "D")
        if waybill_data.get("is_partial", False) and split_code in ["P", "D"]:
            self.create_partial_waybill_from_xffm(manifest_id, waybill_data, awb_data)

        return awb_id

    def create_partial_waybill_from_xffm(
        self, manifest_id, waybill_data, master_awb_data
    ):
        """
        Create a partial waybill record when XFFM contains transport split code "P".

        Args:
            manifest_id (str): Flight manifest ID.
            waybill_data (dict): Original waybill data from XFFM.
            master_awb_data (dict): Master AWB data that was saved.
        """
        awb_number = waybill_data.get("awb_number")
        if not awb_number:
            return None

        try:
            # Generate sequential partial ID in format PWB-{awb-number}-{sequence}
            partial_id = self._generate_sequential_partial_id(awb_number)

            # Prepare partial waybill data
            partial_data = {
                "partial_id": partial_id,
                "master_awb_number": awb_number,
                "house_awb_number": None,  # XFFM is for master AWBs
                "manifest_id": manifest_id,
                "total_pieces_in_awb": master_awb_data.get("total_pieces", 0),
                "total_weight_in_awb": master_awb_data.get("total_weight", 0.0),
                "weight_unit": master_awb_data.get("weight_unit", "KGM"),
                "origin_airport": master_awb_data.get("origin_airport"),
                "destination_airport": master_awb_data.get("destination_airport"),
                "expected_pieces": waybill_data.get("pieces", 0),
                "expected_weight": waybill_data.get("weight", 0.0),
                "received_pieces": 0,  # Not yet received
                "received_weight": 0.0,  # Not yet received
                "remaining_pieces": waybill_data.get("pieces", 0),
                "remaining_weight": waybill_data.get("weight", 0.0),
                "split_code": "P",
                "split_sequence": 1,  # First partial from XFFM
                "consolidation_id": None,
                "status": "PENDING",
                "source": "XFFM",
                "reconciled_at": None,
                "reconciliation_notes": None,
                "reconciled_by": None,
                "piece_tolerance": None,
                "weight_tolerance": None,
                "mra_success": master_awb_data.get(
                    "mra_success"
                ),  # Inherit from master
                "special_handling_code": waybill_data.get("special_handling_code"),
                "storage_location": None,
                "notes": "Created from XFFM partial shipment (transport split code: P)",
                "uld_id": None,  # Will be set when allocated to ULD
                "branch_id": self.branch_id,
            }

            # Add timestamps
            self.add_timestamps(partial_data, self.user_id, self.user_id)

            # Insert partial waybill record
            partial_id_db = self.insert_record("partial_waybills", partial_data, "id")

            self.logger.info(
                f"Created partial waybill {partial_id} for AWB {awb_number} from XFFM (ID: {partial_id_db})"
            )

            return partial_id_db

        except Exception as e:
            self.logger.error(
                f"Error creating partial waybill for AWB {awb_number}: {e}"
            )
            return None

    def get_current_timestamp(self):
        """Get current timestamp in ISO format."""
        from datetime import datetime

        return datetime.now().isoformat()

    def update_manifest_status(self, manifest_id, status, notes=None):
        """
        Update flight manifest status.

        Args:
            manifest_id (str): Manifest ID.
            status (str): New status.
            notes (str): Optional status notes.

        Returns:
            int: Number of affected rows.
        """
        update_data = {"status": status, "updated_by": self.user_id}

        if notes:
            update_data["status_notes"] = notes

        self.add_timestamps(update_data, updated_by=self.user_id)

        return self.update_record(
            "flight_manifests", update_data, "manifest_id = %s", (manifest_id,)
        )

    def get_manifest_summary(self, manifest_id):
        """
        Get flight manifest summary information.

        Args:
            manifest_id (str): Manifest ID.

        Returns:
            dict: Manifest summary data.
        """
        # Get manifest data
        manifest_data = self.find_record(
            "flight_manifests",
            "manifest_id = %s",
            (manifest_id,),
            """manifest_id, flight_number, flight_date, carrier_code,
               departure_airport, arrival_airport, total_pieces, total_weight,
               status, created_at""",
        )

        if not manifest_data:
            return None

        summary = {
            "manifest_id": manifest_data[0],
            "flight_number": manifest_data[1],
            "flight_date": manifest_data[2],
            "carrier_code": manifest_data[3],
            "departure_airport": manifest_data[4],
            "arrival_airport": manifest_data[5],
            "total_pieces": manifest_data[6],
            "total_weight": manifest_data[7],
            "status": manifest_data[8],
            "created_at": manifest_data[9],
        }

        # Get ULD count
        uld_count = self.count_records(
            "uld_details", "manifest_id = %s", (manifest_data[0],)
        )
        summary["uld_count"] = uld_count

        # Get AWB count
        awb_count = self.count_records(
            "uld_awb_allocations",
            "manifest_id = %s",
            (manifest_data[0],),
        )
        summary["awb_count"] = awb_count

        return summary

    def count_records(self, table_name, where_clause, params):
        """
        Count records in a table.

        Args:
            table_name (str): Table name.
            where_clause (str): WHERE clause.
            params (tuple): Query parameters.

        Returns:
            int: Record count.
        """
        try:
            query = f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}"
            self.db_cursor.execute(query, params)
            result = self.db_cursor.fetchone()
            return result[0] if result else 0
        except Exception as e:
            self.logger.error(f"Error counting records in {table_name}: {str(e)}")
            return 0

    def _determine_in_transit_status(self, origin_airport: str, destination_airport: str) -> bool:
        """
        Determine if AWB is in-transit based on origin and destination airports.

        Args:
            origin_airport (str): Origin airport code
            destination_airport (str): Destination airport code

        Returns:
            bool: True if AWB is in-transit, False otherwise
        """
        try:
            # If origin and destination are the same, not in-transit
            if origin_airport == destination_airport:
                return False

            # Get current branch code
            current_branch_query = "SELECT code FROM branches WHERE id = %s"
            self.db_cursor.execute(current_branch_query, (self.branch_id,))
            current_branch_result = self.db_cursor.fetchone()

            if not current_branch_result:
                self.logger.warning(f"Could not find branch code for branch_id {self.branch_id}")
                return False

            current_branch_code = current_branch_result[0]

            # Check if destination has a corresponding branch
            dest_branch_query = "SELECT id FROM branches WHERE code = %s"
            self.db_cursor.execute(dest_branch_query, (destination_airport,))
            dest_branch_result = self.db_cursor.fetchone()

            # In-transit if destination has a branch and it's different from current branch
            return dest_branch_result is not None and destination_airport != current_branch_code

        except Exception as e:
            self.logger.error(f"Error determining in-transit status for {origin_airport} -> {destination_airport}: {str(e)}")
            return False

    def _generate_sequential_partial_id(self, awb_number: str) -> str:
        """
        Generate a sequential partial ID in format PWB-{awb-number}-{sequence}.

        Args:
            awb_number (str): AWB number

        Returns:
            str: Sequential partial ID
        """
        try:
            # Find existing partials for this AWB to determine next sequence number
            existing_query = """
                SELECT partial_id FROM partial_waybills
                WHERE master_awb_number = %s
                AND partial_id LIKE %s
                ORDER BY partial_id
            """
            self.db_cursor.execute(existing_query, (awb_number, f"PWB-{awb_number}-%"))
            existing_partials = self.db_cursor.fetchall()

            # Determine next sequence number
            if not existing_partials:
                sequence = 1
            else:
                # Extract sequence numbers from existing partial IDs
                sequences = []
                for partial in existing_partials:
                    partial_id = partial[0]
                    if partial_id.startswith(f"PWB-{awb_number}-"):
                        try:
                            seq_part = partial_id.split(f"PWB-{awb_number}-")[1]
                            sequences.append(int(seq_part))
                        except (IndexError, ValueError):
                            continue

                sequence = max(sequences) + 1 if sequences else 1

            return f"PWB-{awb_number}-{sequence}"

        except Exception as e:
            self.logger.error(f"Error generating sequential partial ID for AWB {awb_number}: {e}")
            # Fallback to UUID if sequential generation fails
            import uuid
            return f"PWB-{str(uuid.uuid4())[:8]}"
        except Exception as e:
            self.logger.error(f"Error counting records: {e}")
            return 0
