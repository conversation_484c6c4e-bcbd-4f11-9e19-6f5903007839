#!/usr/bin/env python3
"""
XFFM Phase 2 Processor for the Integrated Cargo XML Parser System.

This processor handles the second phase of the two-stage processing system:
- Parses XFFM (Flight Manifest XML) files
- Reconciles incoming flight segments with declared AWB data
- Handles split types (T, S, P, D) and partial shipments
- Manages in-transit shipments and branch-aware logic
- Updates tracking status and piece counts
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from database.integrated_operations import IntegratedDatabaseOperations
from database.fallback_operations import FallbackEnhancedOperations
from extractors.xffm_extractor import XFFMExtractor
from processors.simple_xffm_operations import SimpleXFFMOperations
from services.unified_suffix_service import UnifiedSuffixService


class XFFMPhase2Processor:
    """
    XFFM Phase 2 processor for flight reconciliation.

    Implements the second phase of the integrated cargo processing system:
    1. Parse XFFM XML files
    2. Process each consignment with split type handling
    3. Create orphan AWBs for unverified shipments
    4. Handle partial shipments and ULD splits
    5. Update master waybill tracking status
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """
        Initialize XFFM Phase 2 processor.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID
            user_id (int): Current user ID
            logger: Logger instance
        """
        self.db_ops = FallbackEnhancedOperations(
            db_connection, db_cursor, branch_id, user_id, logger
        )
        self.simple_ops = SimpleXFFMOperations(
            db_connection, db_cursor, branch_id, user_id, logger
        )
        self.extractor = XFFMExtractor(logger)
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.branch_id = branch_id
        self.user_id = user_id
        self.manifest_id = None  # Will be set during processing

        # Initialize unified suffix service for consistent suffix generation
        self.suffix_service = UnifiedSuffixService(
            db_connection, db_cursor, branch_id, logger
        )

    def process_xffm_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process a single XFFM XML file with enhanced business logic.

        Args:
            file_path (str): Path to XFFM XML file

        Returns:
            dict: Processing result with statistics and status
        """
        start_time = time.time()
        file_name = file_path.split('/')[-1]

        # Start enhanced processing log
        log_id = self.db_ops.start_processing_log(file_name, 'XFFM')

        result = {
            'success': False,
            'file_name': file_name,
            'log_id': log_id,
            'message_id': None,
            'awb_count': 0,
            'partial_count': 0,
            'duplicates_skipped': 0,
            'processed_awbs': [],
            'orphan_awbs': [],
            'partial_awbs': [],
            'errors': [],
            'warnings': [],
            'processing_time_ms': 0
        }

        try:
            self.logger.info(f"Starting enhanced XFFM processing for file: {file_path}")

            # Check for file-level duplicates
            import os
            file_hash = self.db_ops.generate_file_hash(file_path)
            existing_file = self.db_ops.check_file_duplicate(file_hash)

            if existing_file:
                self.db_ops.record_duplicate_attempt(file_hash)
                self.db_ops.mark_processing_duplicate(log_id)

                result['success'] = False
                result['message'] = 'Duplicate file detected'
                result['duplicate_info'] = existing_file
                self.logger.info(f"Duplicate XFFM file detected: {file_name}")
                return result

            # Record file as being processed
            file_size = os.path.getsize(file_path)
            self.db_ops.record_file_processed(file_path, file_hash, 'XFFM', file_size)

            # Extract data from XFFM file
            extracted_data = self.extractor.extract_from_file(file_path)

            if not extracted_data or 'success' not in extracted_data or not extracted_data['success']:
                error_msg = f"Failed to extract data from XFFM file: {file_path}"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)
                return result

            # Get message ID for logging
            result['message_id'] = extracted_data.get('message_id', 'UNKNOWN')
            self.manifest_id = extracted_data.get('manifest_id', 'UNKNOWN')

            # Ensure flight manifest exists and commit it immediately
            self.ensure_flight_manifest_exists(extracted_data)

            # Commit the flight manifest creation to avoid foreign key issues
            try:
                self.db_ops.commit_transaction()
                self.logger.info("Flight manifest committed successfully")
            except Exception as commit_error:
                self.logger.error(f"Error committing flight manifest: {commit_error}")
                raise

            # Process each ULD and its consignments
            ulds_data = extracted_data.get('ulds', [])
            if not ulds_data:
                error_msg = "No ULD data found in XFFM file"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                return result

            # CRITICAL FIX: Implement manifest-level AWB aggregation
            # Step 1: Aggregate all AWB data across all ULDs in this manifest
            manifest_awb_aggregations = self.aggregate_awbs_across_manifest(ulds_data, extracted_data)

            # Step 2: Process ULDs (for ULD records and allocations)
            for uld_data in ulds_data:
                uld_result = self.process_uld_consignments(uld_data, extracted_data)

                # Aggregate ULD processing results (but AWB processing is done at manifest level)
                result['errors'].extend(uld_result.get('errors', []))
                result['warnings'].extend(uld_result.get('warnings', []))

            # Step 3: Process aggregated AWBs at manifest level
            for awb_number, aggregated_awb_data in manifest_awb_aggregations.items():
                try:
                    awb_result = self.process_manifest_level_awb(awb_number, aggregated_awb_data, extracted_data)

                    if awb_result.get('awb_number'):
                        if awb_result.get('is_orphan'):
                            result['orphan_awbs'].append(awb_result['awb_number'])
                        else:
                            result['processed_awbs'].append(awb_result['awb_number'])

                    if awb_result.get('partial_id'):
                        result['partial_awbs'].append(awb_result['partial_id'])

                    result['errors'].extend(awb_result.get('errors', []))
                    result['warnings'].extend(awb_result.get('warnings', []))

                except Exception as awb_error:
                    error_msg = f"Error processing manifest-level AWB {awb_number}: {str(awb_error)}"
                    result['errors'].append(error_msg)
                    self.logger.error(error_msg)

            # Calculate final counts
            result['awb_count'] = len(set(result['processed_awbs'] + result['orphan_awbs']))
            result['partial_count'] = len(result['partial_awbs'])

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            result['processing_time_ms'] = processing_time_ms

            # Complete enhanced processing log
            processing_summary = {
                'manifest_id': self.manifest_id,
                'awbs_processed': result['awb_count'],
                'partials_created': result['partial_count'],
                'duplicates_skipped': result['duplicates_skipped']
            }

            # Commit transaction if successful
            if result['awb_count'] > 0:
                self.db_ops.commit_transaction()
                result['success'] = True
                self.db_ops.complete_processing_log(
                    log_id, True, result['awb_count'], 0, None, processing_summary
                )
                self.logger.info(f"Successfully processed XFFM file: {result['file_name']}")
            else:
                self.db_ops.rollback_transaction()
                self.db_ops.complete_processing_log(
                    log_id, False, 0, 0, "No AWBs processed", processing_summary
                )
                self.logger.warning(f"No AWBs processed from file: {result['file_name']}")

        except Exception as e:
            # Rollback transaction on error
            self.db_ops.rollback_transaction()
            error_msg = f"Error processing XFFM file {file_path}: {str(e)}"
            result['errors'].append(error_msg)
            result['processing_time_ms'] = int((time.time() - start_time) * 1000)
            self.logger.error(error_msg, exc_info=True)

            # Complete processing log with error
            self.db_ops.complete_processing_log(log_id, False, error_message=error_msg)

        return result

    def aggregate_awbs_across_manifest(self, ulds_data: List[Dict[str, Any]],
                                     extracted_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        CRITICAL FIX: Aggregate AWB data across all ULDs within the same manifest.
        This ensures that AWBs appearing in multiple ULDs are properly summed before database insertion.

        Args:
            ulds_data (list): List of ULD data containing AWBs
            extracted_data (dict): Complete extracted data

        Returns:
            dict: Manifest-level aggregated AWB data keyed by AWB number
        """
        manifest_awb_aggregations = {}

        self.logger.info(f"Starting manifest-level AWB aggregation for {len(ulds_data)} ULDs")

        for uld_data in ulds_data:
            uld_id = uld_data.get('uld_id', 'UNKNOWN')
            consignments = uld_data.get('awbs', [])

            self.logger.info(f"Processing ULD {uld_id} with {len(consignments)} consignments for aggregation")

            for consignment in consignments:
                awb_number = consignment.get('awb_number')
                if not awb_number:
                    self.logger.warning(f"Consignment in ULD {uld_id} missing AWB number, skipping aggregation")
                    continue

                if awb_number not in manifest_awb_aggregations:
                    # First occurrence of this AWB in the manifest
                    manifest_awb_aggregations[awb_number] = {
                        'awb_number': awb_number,
                        'total_pieces': consignment.get('pieces', 0),
                        'total_weight': consignment.get('weight', 0),
                        'total_volume': consignment.get('volume', 0),
                        'origin_airport': consignment.get('origin_airport', ''),
                        'destination_airport': consignment.get('destination_airport', ''),
                        'special_handling_code': consignment.get('special_handling_code', ''),
                        'description': consignment.get('summary_description', ''),
                        'split_type': consignment.get('transport_split_code', 'T'),
                        'weight_unit': consignment.get('weight_unit', 'KGM'),
                        'volume_unit': consignment.get('volume_unit', ''),
                        'uld_allocations': [{'uld_id': uld_id, 'pieces': consignment.get('pieces', 0), 'weight': consignment.get('weight', 0), 'volume': consignment.get('volume', 0)}],
                        'manifest_entries': [consignment]  # Track all manifest entries for this AWB
                    }
                    self.logger.info(f"First manifest entry for AWB {awb_number}: {consignment.get('pieces', 0)} pieces, {consignment.get('weight', 0)} weight from ULD {uld_id}")
                else:
                    # Subsequent occurrence - aggregate the totals
                    existing = manifest_awb_aggregations[awb_number]
                    existing['total_pieces'] += consignment.get('pieces', 0)
                    existing['total_weight'] += consignment.get('weight', 0)
                    existing['total_volume'] += consignment.get('volume', 0)
                    existing['uld_allocations'].append({'uld_id': uld_id, 'pieces': consignment.get('pieces', 0), 'weight': consignment.get('weight', 0), 'volume': consignment.get('volume', 0)})
                    existing['manifest_entries'].append(consignment)

                    self.logger.info(f"Aggregating AWB {awb_number}: +{consignment.get('pieces', 0)} pieces, +{consignment.get('weight', 0)} weight from ULD {uld_id}. Manifest total: {existing['total_pieces']} pieces, {existing['total_weight']} weight")

        # Log final aggregation summary
        self.logger.info(f"Manifest-level aggregation complete: {len(manifest_awb_aggregations)} unique AWBs")
        for awb_number, data in manifest_awb_aggregations.items():
            self.logger.info(f"AWB {awb_number}: {data['total_pieces']} pieces, {data['total_weight']} weight across {len(data['uld_allocations'])} ULD allocations")

        return manifest_awb_aggregations

    def process_manifest_level_awb(self, awb_number: str, aggregated_awb_data: Dict[str, Any],
                                  full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single AWB using manifest-level aggregated totals.

        Args:
            awb_number (str): AWB number
            aggregated_awb_data (dict): Manifest-level aggregated AWB data
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Processing result for this manifest-level AWB
        """
        result = {
            'awb_number': awb_number,
            'partial_id': None,
            'is_orphan': False,
            'action': None,
            'errors': [],
            'warnings': []
        }

        try:
            self.logger.info(f"Processing manifest-level AWB: {awb_number}, Manifest total pieces: {aggregated_awb_data['total_pieces']}, Manifest total weight: {aggregated_awb_data['total_weight']}, Split: {aggregated_awb_data['split_type']}")

            # Prepare enhanced AWB data using manifest-level aggregated totals
            enhanced_awb_data = self.prepare_enhanced_awb_data_from_manifest_aggregation(aggregated_awb_data, full_extracted_data)

            # Use simple split type handling with manifest-level aggregated data
            split_result = self.handle_simple_split_types(
                enhanced_awb_data,
                aggregated_awb_data['uld_allocations'][0]['uld_id'] if aggregated_awb_data['uld_allocations'] else None,  # Use first ULD for reference
                self.manifest_id
            )

            # Create ULD allocations for all ULDs this AWB appears in
            for uld_allocation in aggregated_awb_data['uld_allocations']:
                try:
                    self.simple_ops.create_uld_awb_allocation(
                        awb_number,
                        uld_allocation['uld_id'],
                        self.manifest_id,
                        {
                            'pieces': uld_allocation['pieces'],
                            'weight': uld_allocation['weight'],
                            'volume': uld_allocation['volume'],
                            'split_type': aggregated_awb_data['split_type'],
                            'description': aggregated_awb_data['description']
                        }
                    )
                except Exception as uld_error:
                    self.logger.error(f"Error creating ULD allocation for AWB {awb_number} in ULD {uld_allocation['uld_id']}: {str(uld_error)}")

            result.update({
                'action': f"MANIFEST_LEVEL_{split_result['split_type']}_AGGREGATED",
                'partial_created': split_result['partial_created'],
                'suffix': split_result.get('suffix'),
                'partial_id': split_result.get('partial_id'),
                'manifest_total_pieces': aggregated_awb_data['total_pieces'],
                'manifest_total_weight': aggregated_awb_data['total_weight'],
                'uld_allocation_count': len(aggregated_awb_data['uld_allocations'])
            })

        except Exception as e:
            error_msg = f"Error processing manifest-level AWB {awb_number}: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg, exc_info=True)

        return result

    def prepare_enhanced_awb_data_from_manifest_aggregation(self, aggregated_awb_data: Dict[str, Any],
                                                           full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare enhanced AWB data from manifest-level aggregated data.

        Args:
            aggregated_awb_data (dict): Manifest-level aggregated AWB data
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Enhanced AWB data using manifest-level aggregated totals
        """
        flight_info = full_extracted_data.get('flight_info', {})

        return {
            'awb_number': aggregated_awb_data.get('awb_number'),
            'manifest_id': self.manifest_id,
            'origin_airport': aggregated_awb_data.get('origin_airport', ''),
            'destination_airport': aggregated_awb_data.get('destination_airport', ''),
            'pieces': aggregated_awb_data.get('total_pieces', 0),  # Manifest-level aggregated total
            'weight': aggregated_awb_data.get('total_weight', 0),  # Manifest-level aggregated total
            'volume': aggregated_awb_data.get('total_volume', 0),  # Manifest-level aggregated total
            'weight_unit': aggregated_awb_data.get('weight_unit', 'KGM'),
            'volume_unit': aggregated_awb_data.get('volume_unit'),
            'description': aggregated_awb_data.get('description', ''),
            'special_handling': aggregated_awb_data.get('special_handling_code', ''),
            'transport_split_description': aggregated_awb_data.get('split_type', 'T'),
            'flight_number': flight_info.get('flight_number', 'UNKNOWN'),
            'flight_date': flight_info.get('flight_date', ''),
            'split_type': aggregated_awb_data.get('split_type', 'T')
        }

    def ensure_flight_manifest_exists(self, extracted_data: Dict[str, Any]):
        """
        Ensure the flight manifest exists in the database.

        Args:
            extracted_data (dict): Complete extracted data from XFFM
        """
        try:
            manifest_id = extracted_data.get('manifest_id')
            if not manifest_id:
                return

            # Check if manifest already exists
            existing = self.db_ops.find_record(
                'flight_manifests',
                'manifest_id = %s',
                (manifest_id,),
                'manifest_id'
            )

            if existing:
                self.logger.debug(f"Flight manifest {manifest_id} already exists")
                return

            # Create flight manifest record
            flight_info = extracted_data.get('flight_info', {})

            # Parse flight date from string if needed
            flight_date = flight_info.get('flight_date')
            if isinstance(flight_date, str):
                from datetime import datetime
                try:
                    flight_date = datetime.strptime(flight_date, '%Y-%m-%d').date()
                except:
                    flight_date = datetime.now().date()

            manifest_data = {
                'manifest_id': manifest_id,
                'flight_number': flight_info.get('flight_number', ''),
                'flight_date': flight_date or 'CURRENT_DATE',
                'carrier_code': flight_info.get('carrier_code'),
                'departure_airport': flight_info.get('departure_airport', ''),
                'arrival_airport': flight_info.get('arrival_airport', ''),
                'scheduled_departure': 'NOW()',  # Default to current time
                'scheduled_arrival': 'NOW()',    # Default to current time
                'flight_type': 'NORMAL',
                'status': 'SCHEDULED',
                'total_pieces': 0,
                'total_weight': 0,
                'branch_id': self.branch_id,
                'created_by': self.user_id,
                'updated_by': self.user_id,
                'xml_data': json.dumps(extracted_data) if extracted_data else None
            }

            # Add timestamps
            self.db_ops.add_timestamps(manifest_data, self.user_id, self.user_id)

            self.db_ops.insert_record('flight_manifests', manifest_data)
            self.logger.info(f"Created flight manifest: {manifest_id}")

        except Exception as e:
            self.logger.error(f"Error ensuring flight manifest exists: {str(e)}")
            # Don't raise the exception as this shouldn't stop processing

    def process_uld_consignments(self, uld_data: Dict[str, Any],
                                full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process all consignments within a ULD with AWB-level aggregation.

        Args:
            uld_data (dict): ULD data with consignments
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Processing result for this ULD
        """
        result = {
            'processed_awbs': [],
            'orphan_awbs': [],
            'partial_awbs': [],
            'errors': [],
            'warnings': [],
            'awb_aggregations': {}  # Track AWB aggregations for this ULD
        }

        try:
            uld_id = uld_data.get('uld_id', 'UNKNOWN')
            self.logger.info(f"Processing ULD: {uld_id}")

            # Create ULD record with error handling
            try:
                uld_record_data = {
                    'uld_id': uld_id,
                    'uld_type': uld_data.get('uld_type'),
                    'uld_owner': uld_data.get('uld_owner'),
                    'manifest_id': full_extracted_data.get('manifest_id'),
                    'current_location': self.db_ops.get_branch_code(self.branch_id),
                    'weight': uld_data.get('total_weight', 0),
                    'pieces': uld_data.get('total_pieces', 0)
                }

                uld_record_id = self.db_ops.create_uld_record(uld_record_data)
                self.logger.info(f"Created ULD record with ID: {uld_record_id}")
            except Exception as uld_error:
                # If ULD creation fails, log error but continue processing consignments
                error_msg = f"Error creating ULD record for {uld_id}: {str(uld_error)}"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                # Don't return - continue processing consignments even if ULD creation failed

            # NOTE: AWB processing is now handled at manifest level for proper aggregation
            # This ULD processing only creates ULD records and tracks consignments for reference
            consignments = uld_data.get('awbs', [])
            self.logger.info(f"ULD {uld_id} contains {len(consignments)} consignments (AWB processing handled at manifest level)")

            # Track consignments for reference but don't process AWBs here
            result['consignment_count'] = len(consignments)
            result['awb_numbers_in_uld'] = [c.get('awb_number') for c in consignments if c.get('awb_number')]

        except Exception as e:
            error_msg = f"Error processing ULD {uld_data.get('uld_id', 'UNKNOWN')}: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg, exc_info=True)

        return result

    def aggregate_awb_data_in_uld(self, consignments: List[Dict[str, Any]], uld_id: str) -> Dict[str, Dict[str, Any]]:
        """
        Aggregate AWB data within a single ULD.

        Args:
            consignments (list): List of consignment data from ULD
            uld_id (str): ULD ID for logging

        Returns:
            dict: Aggregated AWB data keyed by AWB number
        """
        awb_aggregations = {}

        for consignment in consignments:
            awb_number = consignment.get('awb_number')
            if not awb_number:
                self.logger.warning(f"Consignment in ULD {uld_id} missing AWB number, skipping")
                continue

            if awb_number not in awb_aggregations:
                # First occurrence of this AWB in this ULD
                awb_aggregations[awb_number] = {
                    'awb_number': awb_number,
                    'pieces': consignment.get('pieces', 0),
                    'weight': consignment.get('weight', 0),
                    'volume': consignment.get('volume', 0),
                    'origin_airport': consignment.get('origin_airport', ''),
                    'destination_airport': consignment.get('destination_airport', ''),
                    'special_handling_code': consignment.get('special_handling_code', ''),
                    'description': consignment.get('summary_description', ''),
                    'split_type': consignment.get('transport_split_code', 'T'),
                    'weight_unit': consignment.get('weight_unit', 'KGM'),
                    'volume_unit': consignment.get('volume_unit', ''),
                    'uld_entries': [consignment]  # Track all entries for this AWB
                }
                self.logger.info(f"First entry for AWB {awb_number} in ULD {uld_id}: {consignment.get('pieces', 0)} pieces, {consignment.get('weight', 0)} weight")
            else:
                # Subsequent occurrence - aggregate the totals
                existing = awb_aggregations[awb_number]
                existing['pieces'] += consignment.get('pieces', 0)
                existing['weight'] += consignment.get('weight', 0)
                existing['volume'] += consignment.get('volume', 0)
                existing['uld_entries'].append(consignment)

                self.logger.info(f"Aggregating AWB {awb_number} in ULD {uld_id}: +{consignment.get('pieces', 0)} pieces, +{consignment.get('weight', 0)} weight. Total: {existing['pieces']} pieces, {existing['weight']} weight")

        self.logger.info(f"ULD {uld_id} aggregation complete: {len(awb_aggregations)} unique AWBs")
        return awb_aggregations

    def process_aggregated_awb(self, awb_number: str, aggregated_data: Dict[str, Any],
                              uld_id: str, full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single aggregated AWB (sum of all ULD entries for this AWB).

        Args:
            awb_number (str): AWB number
            aggregated_data (dict): Aggregated AWB data
            uld_id (str): ULD ID
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Processing result for this aggregated AWB
        """
        result = {
            'awb_number': awb_number,
            'partial_id': None,
            'is_orphan': False,
            'action': None,
            'errors': [],
            'warnings': []
        }

        try:
            self.logger.info(f"Processing aggregated AWB: {awb_number}, Total pieces: {aggregated_data['pieces']}, Total weight: {aggregated_data['weight']}, Split: {aggregated_data['split_type']}")

            # Skip duplicate detection for XFFM files - they represent actual cargo manifests
            # and should always be processed even if similar content was seen before
            self.logger.info(f"Processing aggregated AWB {awb_number} (no duplicate check for XFFM)")

            # Prepare enhanced AWB data using aggregated totals
            enhanced_awb_data = self.prepare_enhanced_awb_data_from_aggregated(aggregated_data, full_extracted_data)

            # Use simple split type handling with aggregated data
            split_result = self.handle_simple_split_types(
                enhanced_awb_data,
                uld_id,
                self.manifest_id
            )

            result.update({
                'action': f"ENHANCED_{split_result['split_type']}_AGGREGATED",
                'partial_created': split_result['partial_created'],
                'suffix': split_result.get('suffix'),
                'partial_id': split_result.get('partial_id'),
                'aggregated_pieces': aggregated_data['pieces'],
                'aggregated_weight': aggregated_data['weight'],
                'uld_entry_count': len(aggregated_data['uld_entries'])
            })

        except Exception as e:
            error_msg = f"Error processing aggregated AWB {awb_number}: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg, exc_info=True)

        return result

    def prepare_enhanced_awb_data_from_aggregated(self, aggregated_data: Dict[str, Any],
                                                 full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare enhanced AWB data from aggregated consignment data.

        Args:
            aggregated_data (dict): Aggregated AWB data
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Enhanced AWB data using aggregated totals
        """
        flight_info = full_extracted_data.get('flight_info', {})

        return {
            'awb_number': aggregated_data.get('awb_number'),
            'manifest_id': self.manifest_id,
            'origin_airport': aggregated_data.get('origin_airport', ''),
            'destination_airport': aggregated_data.get('destination_airport', ''),
            'pieces': aggregated_data.get('pieces', 0),  # Aggregated total
            'weight': aggregated_data.get('weight', 0),  # Aggregated total
            'volume': aggregated_data.get('volume', 0),  # Aggregated total
            'weight_unit': aggregated_data.get('weight_unit', 'KGM'),
            'volume_unit': aggregated_data.get('volume_unit'),
            'description': aggregated_data.get('description', ''),
            'special_handling': aggregated_data.get('special_handling_code', ''),
            'transport_split_description': aggregated_data.get('split_type', 'T'),
            'flight_number': flight_info.get('flight_number', 'UNKNOWN'),
            'flight_date': flight_info.get('flight_date', ''),
            'split_type': aggregated_data.get('split_type', 'T')
        }

    def process_single_consignment(self, consignment: Dict[str, Any], uld_id: str,
                                  full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single consignment with enhanced split type handling.

        Args:
            consignment (dict): Consignment data
            uld_id (str): ULD ID containing this consignment
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Processing result for this consignment
        """
        result = {
            'awb_number': None,
            'partial_id': None,
            'is_orphan': False,
            'action': None,
            'errors': [],
            'warnings': []
        }

        try:
            awb_number = consignment.get('awb_number')
            split_type = consignment.get('transport_split_code', 'T')

            if not awb_number:
                result['errors'].append("Missing AWB number in consignment")
                return result

            result['awb_number'] = awb_number
            self.logger.info(f"Processing consignment AWB: {awb_number}, Split: {split_type}")

            # Skip duplicate detection for XFFM files - they represent actual cargo manifests
            # and should always be processed even if similar content was seen before
            self.logger.info(f"Processing AWB {awb_number} (no duplicate check for XFFM)")

            # Prepare enhanced AWB data
            enhanced_awb_data = self.prepare_enhanced_awb_data(consignment, full_extracted_data)

            # Use simple split type handling (fallback to old method)
            split_result = self.handle_simple_split_types(
                enhanced_awb_data,
                uld_id,
                self.manifest_id
            )

            result.update({
                'action': f"ENHANCED_{split_result['split_type']}",
                'partial_created': split_result['partial_created'],
                'suffix': split_result.get('suffix'),
                'partial_id': split_result.get('partial_id')
            })

        except Exception as e:
            error_msg = f"Error processing consignment {consignment.get('awb_number', 'UNKNOWN')}: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg, exc_info=True)

        return result

    def handle_simple_split_types(self, awb_data: Dict[str, Any], uld_id: str, manifest_id: str) -> Dict[str, Any]:
        """
        Simple split type handling that works with existing database schema.

        Args:
            awb_data (dict): AWB data
            uld_id (str): ULD ID
            manifest_id (str): Manifest ID

        Returns:
            dict: Processing result
        """
        result = {
            'split_type': awb_data.get('split_type', 'T'),
            'partial_created': False,
            'suffix': None,
            'partial_id': None
        }

        awb_number = awb_data['awb_number']
        split_type = awb_data.get('split_type', 'T')

        try:
            self.logger.info(f"Processing split type {split_type} for AWB {awb_number}")

            if split_type in ['P', 'D']:
                # Handle partial shipments - create partial waybill
                self.logger.info(f"Creating partial waybill for {awb_number}")
                partial_id = self.simple_ops.create_partial_waybill(awb_number, manifest_id, uld_id, awb_data)
                if partial_id:
                    result['partial_created'] = True
                    result['partial_id'] = partial_id
                    result['suffix'] = partial_id

                # Also ensure master waybill exists
                self.logger.info(f"Ensuring master waybill exists for {awb_number}")
                self.simple_ops.save_master_waybill(awb_data, manifest_id)

            elif split_type == 'S':
                # Handle ULD split - just create ULD allocation, master should exist
                self.logger.info(f"Creating ULD allocation for {awb_number} in ULD {uld_id}")
                self.simple_ops.create_uld_awb_allocation(awb_number, uld_id, manifest_id, awb_data)

            else:  # split_type == 'T' or default
                # Handle full AWB - create or update master waybill
                self.logger.info(f"Creating master waybill for {awb_number}")
                awb_id = self.simple_ops.save_master_waybill(awb_data, manifest_id)
                self.logger.info(f"✅ Created master waybill: {awb_id}")

                if uld_id:
                    self.logger.info(f"Creating ULD allocation for {awb_number} in ULD {uld_id}")
                    self.simple_ops.create_uld_awb_allocation(awb_number, uld_id, manifest_id, awb_data)

        except Exception as e:
            self.logger.error(f"❌ Error in simple split type handling for {awb_number}: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            # Don't raise - continue processing other AWBs

        return result

    def create_simple_partial_waybill(self, awb_data: Dict[str, Any], manifest_id: str, uld_id: str = None) -> str:
        """
        Create a simple partial waybill using existing database schema.

        Args:
            awb_data (dict): AWB data
            manifest_id (str): Manifest ID
            uld_id (str): ULD ID if applicable

        Returns:
            str: Partial ID if created, None otherwise
        """
        awb_number = awb_data['awb_number']

        try:
            # Check if partial already exists for this AWB on this manifest
            existing = self.db_ops.find_record(
                'partial_waybills',
                'master_awb_number = %s AND manifest_id = %s',
                (awb_number, manifest_id),
                'partial_id'
            )

            if existing:
                self.logger.info(f"Partial already exists for AWB {awb_number} on manifest {manifest_id}")
                return existing[0]

            # Get next sequence number for this AWB
            count_result = self.db_ops.execute_query(
                "SELECT COUNT(*) FROM partial_waybills WHERE master_awb_number = %s",
                (awb_number,),
                fetch_one=True
            )

            sequence = (count_result[0] if count_result else 0) + 1
            partial_id = f"PWB-{awb_number}-{sequence}"

            # Prepare partial data
            partial_data = {
                'partial_id': partial_id,
                'master_awb_number': awb_number,
                'manifest_id': manifest_id,
                'origin_airport': awb_data.get('origin_airport', ''),
                'destination_airport': awb_data.get('destination_airport', ''),
                'expected_pieces': awb_data.get('pieces', 0),
                'expected_weight': awb_data.get('weight', 0),
                'received_pieces': awb_data.get('pieces', 0),  # For XFFM, assume received = expected
                'received_weight': awb_data.get('weight', 0),
                'remaining_pieces': 0,
                'remaining_weight': 0,
                'split_code': awb_data.get('split_type', 'P'),
                'split_sequence': sequence,
                'status': 'CHECKED_IN',  # For XFFM, mark as checked in
                'special_handling_code': awb_data.get('special_handling_code'),
                'summary_description': awb_data.get('description'),
                'uld_id': uld_id,
                'weight_unit': awb_data.get('weight_unit', 'KGM'),
                'gross_volume': awb_data.get('volume'),
                'volume_unit': awb_data.get('volume_unit'),
                'source': 'XFFM',
                'branch_id': self.branch_id,
            }

            # Add timestamps
            self.db_ops.add_timestamps(partial_data, self.user_id, self.user_id)

            # Insert partial waybill
            self.db_ops.insert_record('partial_waybills', partial_data)

            self.logger.info(f"Created simple partial waybill: {partial_id}")
            return partial_id

        except Exception as e:
            self.logger.error(f"Error creating simple partial waybill for {awb_number}: {str(e)}")
            return None

    def ensure_master_waybill_exists(self, awb_data: Dict[str, Any], manifest_id: str) -> None:
        """
        Ensure master waybill exists, create orphan if needed.

        Args:
            awb_data (dict): AWB data
            manifest_id (str): Manifest ID
        """
        awb_number = awb_data['awb_number']

        try:
            # Check if master waybill exists
            existing = self.db_ops.find_record(
                'master_waybills',
                'awb_number = %s',
                (awb_number,),
                'awb_id'
            )

            if not existing:
                # Create orphan master waybill
                orphan_data = {
                    'awb_number': awb_number,
                    'type_code': '740',
                    'manifest_id': manifest_id,
                    'origin_airport': awb_data.get('origin_airport', ''),
                    'destination_airport': awb_data.get('destination_airport', ''),
                    'total_pieces': awb_data.get('pieces', 0),
                    'total_weight': awb_data.get('weight', 0),
                    'weight_unit': awb_data.get('weight_unit', 'KGM'),
                    'gross_volume': awb_data.get('volume'),
                    'volume_unit': awb_data.get('volume_unit'),
                    'summary_description': awb_data.get('description'),
                    'special_handling_code': awb_data.get('special_handling_code'),
                    'status': 'UNVERIFIED_ORPHAN',
                    'is_placeholder': True,
                    'in_transit': False,
                    'total_pieces_expected': awb_data.get('pieces', 0),
                    'received_pieces_count': 0,
                    'branch_id': self.branch_id,
                }

                self.db_ops.create_or_update_master_waybill(orphan_data, is_orphan=True)
                self.logger.info(f"Created orphan master waybill for {awb_number}")

        except Exception as e:
            self.logger.error(f"Error ensuring master waybill exists for {awb_number}: {str(e)}")

    def prepare_enhanced_awb_data(self, consignment: Dict[str, Any],
                                 full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare enhanced AWB data for processing.

        Args:
            consignment (dict): Consignment data
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Enhanced AWB data
        """
        flight_info = full_extracted_data.get('flight_info', {})

        return {
            'awb_number': consignment.get('awb_number'),
            'manifest_id': self.manifest_id,
            'origin_airport': consignment.get('origin_airport', ''),
            'destination_airport': consignment.get('destination_airport', ''),
            'pieces': consignment.get('pieces', 0),
            'weight': consignment.get('weight', 0),
            'volume': consignment.get('volume', 0),
            'weight_unit': consignment.get('weight_unit', 'KGM'),
            'volume_unit': consignment.get('volume_unit'),
            'description': consignment.get('summary_description', ''),
            'special_handling': consignment.get('special_handling_code', ''),
            'transport_split_description': consignment.get('transport_split_code', 'T'),
            'flight_number': flight_info.get('flight_number', 'UNKNOWN'),
            'flight_date': flight_info.get('flight_date', ''),
            'split_type': consignment.get('transport_split_code', 'T')
        }

    def handle_full_awb(self, consignment: Dict[str, Any], uld_id: str,
                       full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle full AWB (split type 'T').

        Args:
            consignment (dict): Consignment data
            uld_id (str): ULD ID
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Processing result
        """
        result = {'action': 'FULL_AWB', 'is_orphan': False}
        awb_number = consignment['awb_number']

        try:
            # Check if AWB exists in master_waybills
            existing = self.db_ops.find_record(
                'master_waybills',
                'awb_number = %s',
                (awb_number,),
                'awb_id, status'
            )

            if not existing:
                # Create orphan AWB placeholder
                awb_data = self.prepare_orphan_awb_data(consignment, full_extracted_data)
                awb_number, was_created = self.db_ops.create_or_update_master_waybill(
                    awb_data, is_orphan=True
                )
                result['is_orphan'] = True
                result['action'] = 'CREATED_ORPHAN'
                self.logger.info(f"Created orphan AWB: {awb_number}")

            # Update received pieces count
            pieces = consignment.get('pieces', 0)
            if pieces > 0:
                self.db_ops.update_master_waybill_pieces(awb_number, pieces)

            # Create ULD allocation record (using existing uld_awbs table)
            self.create_uld_awb_allocation(awb_number, uld_id, consignment)

        except Exception as e:
            result['errors'] = [f"Error handling full AWB {awb_number}: {str(e)}"]
            self.logger.error(result['errors'][0], exc_info=True)

        return result

    def handle_uld_split_awb(self, consignment: Dict[str, Any], uld_id: str,
                            full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle ULD-split AWB (split type 'S').

        Args:
            consignment (dict): Consignment data
            uld_id (str): ULD ID
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Processing result
        """
        result = {'action': 'ULD_SPLIT', 'is_orphan': False}
        awb_number = consignment['awb_number']

        try:
            # For split type 'S', only insert ULD entry, do not create master
            # The master should already exist from a previous 'T' type or XFWB

            # Create ULD allocation record
            self.create_uld_awb_allocation(awb_number, uld_id, consignment)

            # Update received pieces count if master exists
            pieces = consignment.get('pieces', 0)
            if pieces > 0:
                existing = self.db_ops.find_record(
                    'master_waybills',
                    'awb_number = %s',
                    (awb_number,),
                    'awb_id'
                )

                if existing:
                    self.db_ops.update_master_waybill_pieces(awb_number, pieces)
                else:
                    result['warnings'] = [f"Master AWB {awb_number} not found for ULD split"]

        except Exception as e:
            result['errors'] = [f"Error handling ULD split AWB {awb_number}: {str(e)}"]
            self.logger.error(result['errors'][0], exc_info=True)

        return result

    def handle_partial_awb(self, consignment: Dict[str, Any], uld_id: str,
                          full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle partial AWB (split types 'P' and 'D').

        Args:
            consignment (dict): Consignment data
            uld_id (str): ULD ID
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Processing result
        """
        result = {'action': 'PARTIAL_AWB', 'is_orphan': False}
        awb_number = consignment['awb_number']

        try:
            # Get next sequence number for this AWB
            sequence = self.db_ops.get_partial_sequence_number(awb_number)
            suffix = f"-{sequence}"

            # Prepare partial data
            partial_data = {
                'manifest_id': full_extracted_data.get('manifest_id'),
                'origin_airport': consignment.get('origin_airport', ''),
                'destination_airport': consignment.get('destination_airport', ''),
                'pieces': consignment.get('pieces', 0),
                'weight': consignment.get('weight', 0),
                'volume': consignment.get('volume'),
                'weight_unit': consignment.get('weight_unit', 'KGM'),
                'volume_unit': consignment.get('volume_unit'),
                'summary_description': consignment.get('summary_description'),
                'special_handling_code': consignment.get('special_handling_code'),
                'split_code': consignment.get('transport_split_code', 'P'),
                'split_sequence': sequence
            }

            # Create partial waybill record
            partial_id = self.db_ops.create_partial_waybill(
                awb_number, partial_data, suffix, uld_id
            )
            result['partial_id'] = partial_id

            # Create ULD allocation for the partial
            self.create_uld_awb_allocation(partial_id, uld_id, consignment)

            # Update master waybill pieces count
            pieces = consignment.get('pieces', 0)
            if pieces > 0:
                # Ensure master AWB exists (create orphan if needed)
                existing = self.db_ops.find_record(
                    'master_waybills',
                    'awb_number = %s',
                    (awb_number,),
                    'awb_id'
                )

                if not existing:
                    # Create orphan master AWB
                    awb_data = self.prepare_orphan_awb_data(consignment, full_extracted_data)
                    self.db_ops.create_or_update_master_waybill(awb_data, is_orphan=True)
                    result['is_orphan'] = True

                self.db_ops.update_master_waybill_pieces(awb_number, pieces)

            self.logger.info(f"Created partial waybill: {partial_id}")

        except Exception as e:
            result['errors'] = [f"Error handling partial AWB {awb_number}: {str(e)}"]
            self.logger.error(result['errors'][0], exc_info=True)

        return result

    def prepare_orphan_awb_data(self, consignment: Dict[str, Any],
                               full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare orphan AWB data from consignment information.

        Args:
            consignment (dict): Consignment data
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Prepared AWB data for orphan creation
        """
        # Get current branch code for in-transit logic
        current_branch_code = self.db_ops.get_branch_code(self.branch_id)
        destination_code = consignment.get('destination_airport', '')

        # Determine if this is an in-transit shipment
        is_in_transit = False
        if destination_code != current_branch_code:
            # Check if destination has a branch
            dest_branch_id = self.db_ops.check_destination_branch_exists(destination_code)
            is_in_transit = dest_branch_id is not None

        return {
            'awb_number': consignment['awb_number'],
            'type_code': '740',  # Default for orphan AWBs
            'manifest_id': full_extracted_data.get('manifest_id'),
            'origin_airport': consignment.get('origin_airport', ''),
            'destination_airport': consignment.get('destination_airport', ''),
            'total_pieces': consignment.get('pieces', 0),
            'total_weight': consignment.get('weight', 0),
            'weight_unit': consignment.get('weight_unit', 'KGM'),
            'gross_volume': consignment.get('volume'),
            'volume_unit': consignment.get('volume_unit'),
            'summary_description': consignment.get('summary_description'),
            'special_handling_code': consignment.get('special_handling_code'),
            'special_handling_codes': consignment.get('special_handling_codes', []),
            'goods_descriptions': [],
            'in_transit': is_in_transit,
            'xml_data': full_extracted_data
        }

    def create_uld_awb_allocation(self, awb_identifier: str, uld_id: str,
                                 consignment: Dict[str, Any]):
        """
        Create ULD-AWB allocation record.

        Args:
            awb_identifier (str): AWB number or partial ID
            uld_id (str): ULD ID
            consignment (dict): Consignment data
        """
        try:
            # Check if allocation already exists
            existing = self.db_ops.find_record(
                'uld_awbs',
                'uld_id = %s AND awb_number = %s',
                (uld_id, awb_identifier),
                'id'
            )

            if existing:
                self.logger.info(f"ULD allocation already exists for {awb_identifier} in {uld_id}")
                return

            # Create new allocation
            allocation_data = {
                'uld_id': uld_id,
                'manifest_id': self.manifest_id,
                'awb_number': awb_identifier,
                'pieces': consignment.get('pieces', 0),
                'weight': consignment.get('weight', 0),
                'volume': consignment.get('gross_volume'),
                'volume_unit': consignment.get('volume_unit'),
                'split_code': consignment.get('transport_split_code', 'T'),
                'description': consignment.get('summary_description'),
                'summary_description': consignment.get('summary_description'),
                'branch_id': self.branch_id,
            }

            # Add timestamps and user info
            self.db_ops.add_timestamps(allocation_data, self.user_id, self.user_id)

            self.db_ops.insert_record('uld_awbs', allocation_data)
            self.logger.info(f"Created ULD allocation: {awb_identifier} -> {uld_id}")

        except Exception as e:
            self.logger.error(f"Error creating ULD allocation for {awb_identifier}: {str(e)}")

    def log_processing_activity(self, result: Dict[str, Any]):
        """
        Log processing activity to the processing_log table.

        Args:
            result (dict): Processing result
        """
        try:
            self.db_ops.log_processing(
                file_name=result['file_name'],
                message_id=result['message_id'],
                msg_type='XFFM',
                awb_count=result['awb_count'],
                partial_count=result['partial_count'],
                processing_time_ms=result['processing_time_ms'],
                success=result['success'],
                error_message='; '.join(result['errors']) if result['errors'] else None
            )
        except Exception as e:
            self.logger.error(f"Failed to log processing activity: {str(e)}")
